<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>LocalStorage Quota Management Test Suite</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #2c3e50;
            text-align: center;
            margin-bottom: 30px;
        }
        .test-controls {
            text-align: center;
            margin-bottom: 30px;
        }
        .btn {
            background: #3498db;
            color: white;
            border: none;
            padding: 12px 24px;
            margin: 5px;
            border-radius: 5px;
            cursor: pointer;
            font-size: 16px;
        }
        .btn:hover {
            background: #2980b9;
        }
        .btn.danger {
            background: #e74c3c;
        }
        .btn.danger:hover {
            background: #c0392b;
        }
        .btn.success {
            background: #27ae60;
        }
        .btn.success:hover {
            background: #229954;
        }
        .console-output {
            background: #2c3e50;
            color: #ecf0f1;
            padding: 20px;
            border-radius: 5px;
            font-family: 'Courier New', monospace;
            font-size: 14px;
            line-height: 1.4;
            height: 500px;
            overflow-y: auto;
            white-space: pre-wrap;
            margin-bottom: 20px;
        }
        .status-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        .status-card {
            background: #34495e;
            color: white;
            padding: 20px;
            border-radius: 8px;
            text-align: center;
        }
        .status-card h3 {
            margin: 0 0 10px 0;
            font-size: 18px;
        }
        .status-value {
            font-size: 24px;
            font-weight: bold;
            margin: 10px 0;
        }
        .status-warning {
            background: #f39c12;
        }
        .status-critical {
            background: #e74c3c;
        }
        .status-success {
            background: #27ae60;
        }
        .instructions {
            background: #ecf0f1;
            padding: 20px;
            border-radius: 5px;
            margin-bottom: 20px;
        }
        .instructions h3 {
            margin-top: 0;
            color: #2c3e50;
        }
        .alert {
            padding: 15px;
            border-radius: 5px;
            margin-bottom: 20px;
        }
        .alert-info {
            background: #d1ecf1;
            border: 1px solid #bee5eb;
            color: #0c5460;
        }
        .alert-warning {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            color: #856404;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🧪 LocalStorage Quota Management Test Suite</h1>
        
        <div class="alert alert-info">
            <strong>Note:</strong> This test suite validates the LocalStorage Quota Management system that was implemented as part of Task 1.3. 
            Open the browser's Developer Console (F12) to see detailed test output.
        </div>

        <div class="instructions">
            <h3>📋 How to Test:</h3>
            <ol>
                <li><strong>Open Developer Console:</strong> Press F12 and go to the Console tab</li>
                <li><strong>Navigate to Main App:</strong> Go to <a href="/" target="_blank">http://localhost:3000</a> in a new tab</li>
                <li><strong>Run Tests:</strong> In the console, type <code>testQuotaManagement()</code> and press Enter</li>
                <li><strong>Watch Results:</strong> The test suite will run automatically and show results in the console</li>
            </ol>
        </div>

        <div class="test-controls">
            <button class="btn" onclick="openMainApp()">🚀 Open Main Application</button>
            <button class="btn success" onclick="runQuickTest()">⚡ Quick Test</button>
            <button class="btn" onclick="showQuotaInfo()">📊 Show Quota Info</button>
            <button class="btn danger" onclick="clearTestData()">🧹 Clear Test Data</button>
        </div>

        <div class="status-grid" id="statusGrid">
            <div class="status-card">
                <h3>📊 Storage Usage</h3>
                <div class="status-value" id="storageUsage">--%</div>
                <div>of quota used</div>
            </div>
            <div class="status-card">
                <h3>💾 Data Size</h3>
                <div class="status-value" id="dataSize">-- KB</div>
                <div>total storage</div>
            </div>
            <div class="status-card">
                <h3>🏦 Transactions</h3>
                <div class="status-value" id="transactionCount">--</div>
                <div>total transactions</div>
            </div>
            <div class="status-card">
                <h3>🚨 Alerts</h3>
                <div class="status-value" id="alertCount">--</div>
                <div>active alerts</div>
            </div>
        </div>

        <div class="console-output" id="consoleOutput">
🧪 LocalStorage Quota Management Test Suite Ready
==================================================

Instructions:
1. Open the main application: http://localhost:3000
2. Open Developer Console (F12)
3. Run: testQuotaManagement()

The test suite will verify:
✅ Quota monitoring and calculation
✅ Cleanup strategies execution  
✅ Event system integration
✅ Error handling scenarios
✅ Data preservation during cleanup

Click "Quick Test" below to run a simplified test, or follow the instructions above for the full test suite.
        </div>

        <div class="alert alert-warning">
            <strong>⚠️ Important:</strong> This test creates temporary data to test quota management. 
            The test data will be clearly marked with "test_transaction_" prefix and can be safely removed.
        </div>
    </div>

    <script>
        // Console output capture
        const originalLog = console.log;
        const originalError = console.error;
        const originalWarn = console.warn;
        
        function appendToConsole(message, type = 'log') {
            const output = document.getElementById('consoleOutput');
            const timestamp = new Date().toLocaleTimeString();
            const prefix = type === 'error' ? '❌' : type === 'warn' ? '⚠️' : '📝';
            output.textContent += `[${timestamp}] ${prefix} ${message}\n`;
            output.scrollTop = output.scrollHeight;
        }

        // Override console methods to capture output
        console.log = function(...args) {
            originalLog.apply(console, args);
            appendToConsole(args.join(' '), 'log');
        };
        
        console.error = function(...args) {
            originalError.apply(console, args);
            appendToConsole(args.join(' '), 'error');
        };
        
        console.warn = function(...args) {
            originalWarn.apply(console, args);
            appendToConsole(args.join(' '), 'warn');
        };

        function openMainApp() {
            window.open('/', '_blank');
            appendToConsole('Opening main application in new tab...', 'log');
        }

        function runQuickTest() {
            appendToConsole('Running quick test...', 'log');
            
            // Basic availability test
            if (typeof Storage !== "undefined") {
                appendToConsole('✅ LocalStorage is supported', 'log');
                
                // Test storage write/read
                try {
                    const testKey = 'quota_test_' + Date.now();
                    const testData = JSON.stringify({ test: true, timestamp: Date.now() });
                    localStorage.setItem(testKey, testData);
                    const retrieved = localStorage.getItem(testKey);
                    localStorage.removeItem(testKey);
                    
                    if (retrieved === testData) {
                        appendToConsole('✅ LocalStorage read/write test passed', 'log');
                    } else {
                        appendToConsole('❌ LocalStorage read/write test failed', 'error');
                    }
                } catch (error) {
                    appendToConsole('❌ LocalStorage test error: ' + error.message, 'error');
                }
                
                // Calculate current usage
                let totalSize = 0;
                for (let key in localStorage) {
                    if (localStorage.hasOwnProperty(key)) {
                        totalSize += localStorage[key].length + key.length;
                    }
                }
                
                appendToConsole(`📊 Current localStorage usage: ${Math.round(totalSize / 1024)} KB`, 'log');
                updateStatusCards();
                
            } else {
                appendToConsole('❌ LocalStorage is not supported in this browser', 'error');
            }
        }

        function showQuotaInfo() {
            appendToConsole('Checking quota information...', 'log');
            
            // Try to estimate quota
            try {
                if (navigator.storage && navigator.storage.estimate) {
                    navigator.storage.estimate().then(estimate => {
                        const used = estimate.usage || 0;
                        const quota = estimate.quota || 0;
                        const usagePercent = quota > 0 ? (used / quota * 100).toFixed(1) : 'Unknown';
                        
                        appendToConsole(`📊 Storage Estimate:`, 'log');
                        appendToConsole(`   Used: ${Math.round(used / 1024)} KB`, 'log');
                        appendToConsole(`   Quota: ${Math.round(quota / 1024 / 1024)} MB`, 'log');
                        appendToConsole(`   Usage: ${usagePercent}%`, 'log');
                    }).catch(error => {
                        appendToConsole('❌ Could not get storage estimate: ' + error.message, 'error');
                    });
                } else {
                    appendToConsole('⚠️ Storage estimation API not available', 'warn');
                }
            } catch (error) {
                appendToConsole('❌ Error checking quota: ' + error.message, 'error');
            }
            
            updateStatusCards();
        }

        function clearTestData() {
            appendToConsole('Clearing test data...', 'log');
            
            let removedCount = 0;
            const keysToRemove = [];
            
            // Find test data keys
            for (let i = 0; i < localStorage.length; i++) {
                const key = localStorage.key(i);
                if (key && (key.includes('test') || key.includes('quota_test'))) {
                    keysToRemove.push(key);
                }
            }
            
            // Remove test data
            keysToRemove.forEach(key => {
                localStorage.removeItem(key);
                removedCount++;
            });
            
            appendToConsole(`🧹 Removed ${removedCount} test data items`, 'log');
            updateStatusCards();
        }

        function updateStatusCards() {
            // Calculate localStorage usage
            let totalSize = 0;
            let itemCount = 0;
            
            for (let i = 0; i < localStorage.length; i++) {
                const key = localStorage.key(i);
                if (key) {
                    const value = localStorage.getItem(key);
                    if (value) {
                        totalSize += (key.length + value.length) * 2; // UTF-16
                        itemCount++;
                    }
                }
            }
            
            // Count transactions (if any)
            let transactionCount = 0;
            try {
                const transactionsData = localStorage.getItem('tms_transactions');
                if (transactionsData) {
                    const transactions = JSON.parse(transactionsData);
                    transactionCount = Array.isArray(transactions) ? transactions.length : 0;
                }
            } catch (error) {
                // Ignore parsing errors
            }
            
            // Update status cards
            document.getElementById('storageUsage').textContent = '--% (estimate not available)';
            document.getElementById('dataSize').textContent = Math.round(totalSize / 1024) + ' KB';
            document.getElementById('transactionCount').textContent = transactionCount;
            document.getElementById('alertCount').textContent = '-- (app not loaded)';
            
            // Try to get more detailed info if app is loaded
            if (window.opener && window.opener.storageQuotaManager) {
                try {
                    const quotaInfo = window.opener.storageQuotaManager.getQuotaInfo();
                    if (quotaInfo) {
                        document.getElementById('storageUsage').textContent = quotaInfo.utilization.toFixed(1) + '%';
                        
                        // Update card colors based on usage
                        const usageCard = document.getElementById('storageUsage').closest('.status-card');
                        usageCard.className = 'status-card';
                        if (quotaInfo.isCritical) {
                            usageCard.classList.add('status-critical');
                        } else if (quotaInfo.isNearLimit) {
                            usageCard.classList.add('status-warning');
                        } else {
                            usageCard.classList.add('status-success');
                        }
                    }
                    
                    const alerts = window.opener.storageQuotaManager.getActiveAlerts();
                    document.getElementById('alertCount').textContent = alerts.length;
                } catch (error) {
                    // App might not be fully loaded
                }
            }
        }

        // Update status cards every 5 seconds
        setInterval(updateStatusCards, 5000);
        
        // Initial update
        setTimeout(updateStatusCards, 1000);

        appendToConsole('Test interface loaded successfully!', 'log');
        appendToConsole('Use the buttons above to interact with the quota management system.', 'log');
    </script>
    
    <!-- Load the test script -->
    <script src="/test-quota-management.js"></script>
    
    <script>
        // Announce test suite availability
        if (typeof testQuotaManagement === 'function') {
            appendToConsole('✅ Test suite loaded successfully!', 'log');
            appendToConsole('🚀 Run testQuotaManagement() in console or use buttons above', 'log');
        } else {
            appendToConsole('⚠️ Test suite not loaded - check console for errors', 'warn');
        }
    </script>
</body>
</html> 