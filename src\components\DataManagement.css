/* Data Management Component Styles */
.data-management {
  max-width: 1200px;
  margin: 0 auto;
  padding: var(--spacing-lg);
}

.data-management-header {
  text-align: center;
  margin-bottom: var(--spacing-2xl);
}

.data-management-header h2 {
  font-size: 2rem;
  font-weight: 700;
  color: var(--color-neutral-900);
  margin-bottom: var(--spacing-sm);
}

.data-management-header p {
  font-size: 1.125rem;
  color: var(--color-neutral-600);
  margin: 0;
}

/* Loading Indicator */
.loading-indicator {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: var(--spacing-md);
  padding: var(--spacing-2xl);
  background: var(--color-neutral-50);
  border-radius: var(--radius-lg);
  margin: var(--spacing-xl) 0;
}

.loading-indicator .spinner {
  width: 24px;
  height: 24px;
  border: 3px solid var(--color-neutral-300);
  border-top-color: var(--color-primary);
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

.loading-indicator p {
  color: var(--color-neutral-600);
  font-weight: 500;
  margin: 0;
}

/* Storage Info Section */
.storage-info-section {
  display: grid;
  gap: var(--spacing-xl);
}

/* Info Cards */
.info-card,
.files-list-card,
.actions-card,
.warning-card {
  background: white;
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-sm);
  border: 1px solid var(--color-neutral-200);
  padding: var(--spacing-xl);
}

.info-card h3,
.files-list-card h3,
.actions-card h3 {
  font-size: 1.25rem;
  font-weight: 600;
  color: var(--color-neutral-900);
  margin: 0 0 var(--spacing-lg) 0;
}

/* Storage Type */
.storage-type {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--spacing-sm) 0;
  margin-bottom: var(--spacing-md);
  border-bottom: 1px solid var(--color-neutral-200);
}

.type-label {
  font-weight: 600;
  color: var(--color-neutral-700);
}

.type-value {
  font-weight: 600;
  padding: var(--spacing-xs) var(--spacing-sm);
  border-radius: var(--radius-sm);
  font-size: 0.875rem;
}

.type-value.filesystem {
  background: #D1FAE5;
  color: #065F46;
}

.type-value.browser {
  background: #DBEAFE;
  color: #1E40AF;
}

/* Storage Path */
.storage-path {
  background: var(--color-neutral-50);
  padding: var(--spacing-md);
  border-radius: var(--radius-md);
  border: 1px solid var(--color-neutral-200);
  margin-bottom: var(--spacing-lg);
}

.path-label {
  font-weight: 600;
  color: var(--color-neutral-700);
  display: block;
  margin-bottom: var(--spacing-xs);
}

.path-value {
  background: var(--color-neutral-900);
  color: var(--color-neutral-100);
  padding: var(--spacing-xs) var(--spacing-sm);
  border-radius: var(--radius-sm);
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 0.875rem;
  word-break: break-all;
  display: block;
}

/* Storage Details */
.storage-details {
  display: grid;
  gap: var(--spacing-sm);
}

.detail-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--spacing-sm) 0;
  border-bottom: 1px solid var(--color-neutral-200);
}

.detail-item:last-child {
  border-bottom: none;
}

.detail-label {
  font-weight: 500;
  color: var(--color-neutral-700);
}

.detail-value {
  font-weight: 600;
  color: var(--color-neutral-900);
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 0.875rem;
}

/* Files List */
.files-list {
  display: grid;
  gap: var(--spacing-sm);
}

.file-item {
  background: var(--color-neutral-50);
  padding: var(--spacing-md);
  border-radius: var(--radius-md);
  border: 1px solid var(--color-neutral-200);
}

.file-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.file-name {
  font-weight: 500;
  color: var(--color-neutral-900);
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 0.875rem;
}

.file-size {
  font-size: 0.75rem;
  color: var(--color-neutral-500);
}

.no-files {
  text-align: center;
  color: var(--color-neutral-500);
  font-style: italic;
  padding: var(--spacing-lg);
  background: var(--color-neutral-50);
  border-radius: var(--radius-md);
  margin: 0;
}

/* Action Buttons */
.action-buttons {
  display: flex;
  gap: var(--spacing-md);
  flex-wrap: wrap;
  margin-bottom: var(--spacing-lg);
}

.action-buttons .btn {
  display: flex;
  align-items: center;
  gap: var(--spacing-xs);
}

.action-buttons .btn svg {
  width: 16px;
  height: 16px;
}

/* Status Messages */
.status-message {
  padding: var(--spacing-md);
  border-radius: var(--radius-md);
  font-weight: 500;
  margin-top: var(--spacing-md);
}

.status-message.success {
  background: #D1FAE5;
  color: #065F46;
  border: 1px solid #34D399;
}

.status-message.error {
  background: #FEE2E2;
  color: #991B1B;
  border: 1px solid #F87171;
}

.status-message.info {
  background: #DBEAFE;
  color: #1E40AF;
  border: 1px solid #60A5FA;
}

/* Warning Card */
.warning-card {
  background: #FFFBEB;
  border: 1px solid #F59E0B;
}

.warning-card {
  display: flex;
  gap: var(--spacing-md);
  align-items: flex-start;
}

.warning-icon {
  color: #D97706;
  flex-shrink: 0;
}

.warning-icon svg {
  width: 24px;
  height: 24px;
}

.warning-content h4 {
  font-size: 1.125rem;
  font-weight: 600;
  color: #92400E;
  margin: 0 0 var(--spacing-sm) 0;
}

.warning-content p {
  color: #92400E;
  margin: 0;
  line-height: 1.5;
}

/* Responsive Design */
@media (max-width: 768px) {
  .data-management {
    padding: var(--spacing-md);
  }

  .action-buttons {
    flex-direction: column;
  }

  .action-buttons .btn {
    justify-content: center;
  }

  .file-info {
    flex-direction: column;
    align-items: flex-start;
    gap: var(--spacing-xs);
  }

  .detail-item {
    flex-direction: column;
    align-items: flex-start;
    gap: var(--spacing-xs);
  }

  .warning-card {
    flex-direction: column;
  }
}

@keyframes spin {
  to { transform: rotate(360deg); }
} 