/* DataHub Component Styles - Professional Gray Theme */
/* Override any blue colors with professional grays */
.datahub {
  min-height: 100vh;
  background: var(--color-neutral-50);
  /* Local color overrides for professional appearance */
  --local-primary: #1f2937;
  --local-primary-hover: #374151;
  --local-primary-light: #f3f4f6;
  --local-focus-ring: rgba(156, 163, 175, 0.1);
}

/* Ensure no blue colors anywhere in DataHub */
.datahub * {
  --color-primary: #1f2937 !important;
  --color-primary-hover: #374151 !important;
  --color-primary-light: #f3f4f6 !important;
}

.datahub-header {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  padding: 20px 0;
  margin-bottom: var(--spacing-xl);
  color: white;
  position: sticky;
  top: 0;
  z-index: 100;
  backdrop-filter: blur(10px);
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
}

.header-left {
  flex: 1;
}

.app-logo {
  display: flex;
  align-items: center;
  gap: 16px;
}

.logo-icon {
  width: 48px;
  height: 48px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.app-title-section {
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.app-title {
  font-size: 1.5rem;
  font-weight: 700;
  color: white;
  margin: 0;
  line-height: 1.2;
  letter-spacing: -0.02em;
}

.app-subtitle {
  font-size: 0.875rem;
  color: rgba(255, 255, 255, 0.8);
  margin: 0;
  font-weight: 400;
  letter-spacing: 0.01em;
}

.header-right {
  display: flex;
  align-items: center;
  gap: 16px;
}

.system-status {
  display: flex;
  align-items: center;
  gap: 8px;
  background: rgba(255, 255, 255, 0.1);
  padding: 8px 12px;
  border-radius: 20px;
  font-size: 0.75rem;
  font-weight: 500;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.system-status.instant-load {
  background: rgba(34, 197, 94, 0.2);
  color: #dcfce7;
  border-color: rgba(34, 197, 94, 0.3);
}

.status-indicator {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background: #22c55e;
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

.header-actions {
  display: flex;
  align-items: center;
  gap: 8px;
}

.header-btn {
  width: 40px;
  height: 40px;
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 10px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  cursor: pointer;
  transition: all 0.2s ease;
  backdrop-filter: blur(10px);
}

.header-btn:hover {
  background: rgba(255, 255, 255, 0.2);
  border-color: rgba(255, 255, 255, 0.3);
  transform: translateY(-1px);
}

.datahub-content {
  padding-bottom: var(--spacing-3xl);
}

/* Tab Navigation */
.datahub-tabs {
  margin-bottom: var(--spacing-2xl);
}

.tab-list {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));
  gap: 20px;
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
}

.tab-button {
  display: flex;
  align-items: center;
  gap: 20px;
  padding: 24px;
  background: white;
  border: 1px solid var(--color-neutral-200);
  border-radius: 16px;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  text-align: left;
  font-family: inherit;
  width: 100%;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1), 0 1px 2px rgba(0, 0, 0, 0.06);
  position: relative;
  overflow: hidden;
}

.tab-button * {
  background: transparent !important;
  border: none !important;
  box-shadow: none !important;
}

.tab-button .tab-icon {
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%) !important;
}

.tab-button:hover .tab-icon {
  background: linear-gradient(135deg, #f3f4f6 0%, #e5e7eb 100%) !important;
}

.tab-button.active .tab-icon {
  background: linear-gradient(135deg, #e5e7eb 0%, #d1d5db 100%) !important;
}

.tab-button:hover {
  border-color: #d1d5db;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08), 0 2px 4px rgba(0, 0, 0, 0.04);
  transform: translateY(-1px);
}

.tab-button.active,
.tab-button.active.card {
  border-color: #9ca3af !important;
  background: #ffffff !important;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.12), 0 2px 4px rgba(0, 0, 0, 0.06) !important;
  transform: translateY(-1px) !important;
}

.tab-button.active::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 2px;
  background: #1f2937;
  border-radius: 16px 16px 0 0;
  z-index: 1;
}

.tab-icon {
  flex-shrink: 0;
  width: 48px;
  height: 48px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  border-radius: 12px;
  color: var(--color-neutral-600);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  font-size: 20px;
}

.tab-button:hover .tab-icon {
  background: linear-gradient(135deg, #f3f4f6 0%, #e5e7eb 100%);
  color: #374151;
  transform: scale(1.02);
}

.tab-button.active .tab-icon {
  background: linear-gradient(135deg, #e5e7eb 0%, #d1d5db 100%);
  color: #1f2937;
  transform: scale(1.02);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.tab-content {
  flex: 1;
  min-width: 0;
  display: flex;
  flex-direction: column;
  justify-content: center;
  background: transparent !important;
  padding: 0 !important;
  margin: 0 !important;
  border: none !important;
  box-shadow: none !important;
}

.tab-label {
  font-size: 1.125rem;
  font-weight: 600;
  color: var(--color-neutral-900);
  margin: 0 0 4px 0;
  line-height: 1.3;
  letter-spacing: -0.01em;
  background: transparent !important;
  padding: 0 !important;
}

.tab-button.active .tab-label {
  color: #1f2937;
  font-weight: 700;
}

.tab-description {
  font-size: 0.875rem;
  color: var(--color-neutral-600);
  line-height: 1.4;
  margin: 0;
  font-weight: 400;
  letter-spacing: 0.01em;
  background: transparent !important;
  padding: 0 !important;
}

.tab-button.active .tab-description {
  color: var(--color-neutral-700);
}

/* Tab Panel */
.tab-panel {
  max-width: 1200px;
  margin: 0 auto;
}

/* Tab Placeholders */
.tab-placeholder {
  background: white;
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-sm);
  border: 1px solid var(--color-neutral-200);
  padding: var(--spacing-3xl);
  text-align: center;
  max-width: 800px;
  margin: 0 auto;
}

.placeholder-icon {
  width: 80px;
  height: 80px;
  margin: 0 auto var(--spacing-lg);
  display: flex;
  align-items: center;
  justify-content: center;
  background: var(--color-neutral-100);
  border-radius: var(--radius-xl);
  color: var(--color-neutral-400);
}

.tab-placeholder h3 {
  font-size: 1.75rem;
  font-weight: 600;
  color: var(--color-neutral-900);
  margin-bottom: var(--spacing-md);
}

.tab-placeholder > p {
  font-size: 1.125rem;
  color: var(--color-neutral-600);
  margin-bottom: var(--spacing-xl);
  line-height: 1.6;
}

.placeholder-features {
  background: var(--color-neutral-50);
  border-radius: var(--radius-lg);
  padding: var(--spacing-xl);
  text-align: left;
  max-width: 500px;
  margin: 0 auto;
}

.placeholder-features h4 {
  font-size: 1.125rem;
  font-weight: 600;
  color: var(--color-neutral-900);
  margin-bottom: var(--spacing-md);
  text-align: center;
}

.placeholder-features ul {
  list-style: none;
  padding: 0;
  margin: 0;
}

.placeholder-features li {
  padding: var(--spacing-sm) 0;
  color: var(--color-neutral-700);
  position: relative;
  padding-left: var(--spacing-lg);
}

.placeholder-features li::before {
  content: "•";
  color: #6b7280;
  font-weight: bold;
  position: absolute;
  left: 0;
}

/* Additional Professional Styling */
.tab-button:focus {
  outline: none;
  border-color: #9ca3af !important;
  box-shadow: 0 0 0 3px rgba(156, 163, 175, 0.1), 0 4px 12px rgba(0, 0, 0, 0.08) !important;
  background: #ffffff !important;
}

.tab-button:active {
  transform: translateY(0);
}

/* Remove all white boxes and internal styling - Override global card styles */
.tab-button,
.tab-button.card {
  background: white !important;
  border: 1px solid var(--color-neutral-200) !important;
  border-radius: 16px !important;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1), 0 1px 2px rgba(0, 0, 0, 0.06) !important;
  overflow: hidden !important;
}

.tab-button > *:not(.tab-icon) {
  background: transparent !important;
  border: none !important;
  box-shadow: none !important;
  padding: 0 !important;
  margin: 0 !important;
}

.tab-button .tab-content,
.tab-button .tab-content > *,
.tab-button .tab-label,
.tab-button .tab-description {
  background: transparent !important;
  border: none !important;
  box-shadow: none !important;
  padding: 0 !important;
  margin: 0 !important;
}

.tab-button .tab-content .tab-label {
  margin-bottom: 4px !important;
}

/* Ensure icons are always visible */
.tab-icon svg,
.tab-icon i {
  font-size: 20px;
  transition: all 0.3s ease;
}

.tab-button:hover .tab-icon svg,
.tab-button:hover .tab-icon i,
.tab-button.active .tab-icon svg,
.tab-button.active .tab-icon i {
  opacity: 1 !important;
  visibility: visible !important;
}

/* Responsive Design */
@media (max-width: 1024px) {
  .tab-list {
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 16px;
    padding: 0 16px;
  }
}

@media (max-width: 768px) {
  .datahub-header {
    padding: 16px 0;
  }

  .header-content {
    padding: 0 16px;
  }

  .app-logo {
    gap: 12px;
  }

  .logo-icon {
    width: 40px;
    height: 40px;
  }

  .app-title {
    font-size: 1.25rem;
  }

  .app-subtitle {
    font-size: 0.75rem;
  }

  .header-right {
    gap: 12px;
  }

  .system-status {
    padding: 6px 10px;
    font-size: 0.7rem;
  }

  .header-btn {
    width: 36px;
    height: 36px;
  }

  .header-btn svg {
    width: 16px;
    height: 16px;
  }

  .tab-list {
    grid-template-columns: 1fr;
    gap: 12px;
    padding: 0 12px;
  }

  .tab-button {
    padding: 20px;
    gap: 16px;
  }

  .tab-icon {
    width: 44px;
    height: 44px;
    font-size: 18px;
  }

  .tab-label {
    font-size: 1rem;
  }

  .tab-description {
    font-size: 0.8rem;
  }

  .tab-placeholder {
    padding: var(--spacing-xl);
  }

  .tab-placeholder h3 {
    font-size: 1.5rem;
  }

  .tab-placeholder > p {
    font-size: 1rem;
  }
}

/* Coming Soon Component Styles */
.coming-soon-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 400px;
  padding: 2rem;
  text-align: center;
  background: #f8f9fa;
  border: 2px dashed #dee2e6;
  border-radius: 12px;
  color: #6c757d;
}

.coming-soon-icon {
  margin-bottom: 1.5rem;
  padding: 1rem;
  background: #e9ecef;
  border-radius: 50%;
  color: #adb5bd;
}

.coming-soon-container h2 {
  margin: 0 0 1rem 0;
  font-size: 1.5rem;
  font-weight: 600;
  color: #495057;
}

.coming-soon-container p {
  margin: 0 0 1.5rem 0;
  font-size: 1rem;
  line-height: 1.5;
  max-width: 500px;
}

.coming-soon-status {
  margin-top: auto;
}

.status-badge {
  display: inline-block;
  padding: 0.5rem 1rem;
  background: #ffc107;
  color: #000;
  border-radius: 20px;
  font-size: 0.875rem;
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}