@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');

/* Reset and base styles */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

html {
  font-size: 16px;
  scroll-behavior: smooth;
}

body {
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
  background: #fafafa;
  color: #1a1a1a;
  line-height: 1.6;
  min-height: 100vh;
  font-feature-settings: 'cv11', 'ss01';
  font-variant-numeric: oldstyle-nums;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* Design system colors */
:root {
  --color-primary: #007AFF;
  --color-primary-hover: #0056CC;
  --color-primary-light: #E6F3FF;
  --color-secondary: #5856D6;
  --color-success: #30D158;
  --color-warning: #FF9500;
  --color-warning-light: #FFF4E6;
  --color-warning-dark: #CC7700;
  --color-error: #FF3B30;
  --color-info: #007AFF;
  --color-info-light: #E6F3FF;
  --color-info-dark: #0056CC;
  --color-neutral-50: #FAFAFA;
  --color-neutral-100: #F5F5F5;
  --color-neutral-200: #E5E5E5;
  --color-neutral-300: #D4D4D4;
  --color-neutral-400: #A3A3A3;
  --color-neutral-500: #737373;
  --color-neutral-600: #525252;
  --color-neutral-700: #404040;
  --color-neutral-800: #262626;
  --color-neutral-900: #171717;
  
  --shadow-xs: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  --shadow-sm: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
  --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
  
  --radius-sm: 6px;
  --radius-md: 8px;
  --radius-lg: 12px;
  --radius-xl: 16px;
  --radius-2xl: 20px;
  --radius-full: 50%;
  
  --spacing-xs: 4px;
  --spacing-sm: 8px;
  --spacing-md: 16px;
  --spacing-lg: 24px;
  --spacing-xl: 32px;
  --spacing-2xl: 48px;
  --spacing-3xl: 64px;
}

/* Layout components */
.container {
  max-width: 1400px;
  margin: 0 auto;
  padding: 0 var(--spacing-lg);
}

.page-header {
  background: white;
  border-bottom: 1px solid var(--color-neutral-200);
  padding: var(--spacing-lg) 0;
  margin-bottom: var(--spacing-xl);
}

.page-title {
  font-size: 2rem;
  font-weight: 700;
  color: var(--color-neutral-900);
  margin-bottom: var(--spacing-sm);
}

.page-subtitle {
  font-size: 1.125rem;
  color: var(--color-neutral-600);
  font-weight: 400;
}

/* Card components */
.card {
  background: white;
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-sm);
  border: 1px solid var(--color-neutral-200);
  overflow: hidden;
  transition: all 0.2s ease;
}

.card:hover {
  box-shadow: var(--shadow-md);
  border-color: var(--color-neutral-300);
}

.card-header {
  padding: var(--spacing-lg);
  border-bottom: 1px solid var(--color-neutral-200);
  background: var(--color-neutral-50);
}

.card-title {
  font-size: 1.25rem;
  font-weight: 600;
  color: var(--color-neutral-900);
  margin-bottom: var(--spacing-xs);
}

.card-description {
  color: var(--color-neutral-600);
  font-size: 0.875rem;
}

.card-content {
  padding: var(--spacing-lg);
}

.card-footer {
  padding: var(--spacing-lg);
  border-top: 1px solid var(--color-neutral-200);
  background: var(--color-neutral-50);
}

/* Button components */
.btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: var(--spacing-sm);
  padding: 10px 16px;
  border-radius: var(--radius-md);
  border: none;
  font-size: 0.875rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  text-decoration: none;
  font-family: inherit;
  line-height: 1.25;
  min-height: 40px;
}

.btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.btn-primary {
  background: var(--color-primary);
  color: white;
}

.btn-primary:hover:not(:disabled) {
  background: var(--color-primary-hover);
  transform: translateY(-1px);
  box-shadow: var(--shadow-md);
}

.btn-secondary {
  background: var(--color-neutral-100);
  color: var(--color-neutral-700);
  border: 1px solid var(--color-neutral-300);
}

.btn-secondary:hover:not(:disabled) {
  background: var(--color-neutral-200);
  border-color: var(--color-neutral-400);
}

.btn-success {
  background: var(--color-success);
  color: white;
}

.btn-success:hover:not(:disabled) {
  background: #28CD14;
  transform: translateY(-1px);
  box-shadow: var(--shadow-md);
}

.btn-danger {
  background: var(--color-error);
  color: white;
}

.btn-outline {
  background: transparent;
  color: var(--color-neutral-700);
  border: 1px solid var(--color-neutral-300);
}

.btn-outline:hover:not(:disabled) {
  background: var(--color-neutral-50);
  border-color: var(--color-neutral-400);
}

.btn-danger:hover:not(:disabled) {
  background: #FF2D20;
  transform: translateY(-1px);
  box-shadow: var(--shadow-md);
}

.btn-lg {
  padding: 12px 24px;
  font-size: 1rem;
  min-height: 48px;
}

.btn-sm {
  padding: 6px 12px;
  font-size: 0.75rem;
  min-height: 32px;
}

/* Form components */
.form-group {
  margin-bottom: var(--spacing-lg);
}

.form-label {
  display: block;
  font-size: 0.875rem;
  font-weight: 500;
  color: var(--color-neutral-700);
  margin-bottom: var(--spacing-sm);
}

.form-input {
  width: 100%;
  padding: 10px 12px;
  border: 1px solid var(--color-neutral-300);
  border-radius: var(--radius-md);
  font-size: 0.875rem;
  transition: all 0.2s ease;
  font-family: inherit;
  background: white;
}

.form-input:focus {
  outline: none;
  border-color: var(--color-primary);
  box-shadow: 0 0 0 3px var(--color-primary-light);
}

.form-input:invalid {
  border-color: var(--color-error);
}

.form-select {
  width: 100%;
  padding: 10px 12px;
  border: 1px solid var(--color-neutral-300);
  border-radius: var(--radius-md);
  font-size: 0.875rem;
  background: white;
  cursor: pointer;
  font-family: inherit;
}

.form-select:focus {
  outline: none;
  border-color: var(--color-primary);
  box-shadow: 0 0 0 3px var(--color-primary-light);
}

.form-error {
  display: block;
  color: var(--color-error);
  font-size: 0.75rem;
  margin-top: var(--spacing-xs);
}

/* Table components */
.table-container {
  overflow-x: auto;
  border: 1px solid var(--color-neutral-200);
  border-radius: var(--radius-lg);
}

.table {
  width: 100%;
  border-collapse: collapse;
  font-size: 0.875rem;
}

.table th {
  background: var(--color-neutral-50);
  padding: 12px 16px;
  text-align: left;
  font-weight: 600;
  color: var(--color-neutral-700);
  border-bottom: 1px solid var(--color-neutral-200);
  white-space: nowrap;
}

.table td {
  padding: 12px 16px;
  border-bottom: 1px solid var(--color-neutral-200);
  color: var(--color-neutral-700);
}

.table tbody tr:hover {
  background: var(--color-neutral-50);
}

.table tbody tr:last-child td {
  border-bottom: none;
}

/* Utility classes */
.text-center { text-align: center; }
.text-right { text-align: right; }
.text-success { color: var(--color-success); }
.text-error { color: var(--color-error); }
.text-warning { color: var(--color-warning); }
.text-muted { color: var(--color-neutral-500); }

.font-mono {
  font-family: 'SF Mono', Monaco, 'Cascadia Code', 'Roboto Mono', Consolas, 'Courier New', monospace;
}

.sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border: 0;
}

/* Responsive design - Desktop first */
@media (max-width: 1200px) {
  .container {
    max-width: 1024px;
  }
}

@media (max-width: 768px) {
  .container {
    padding: 0 var(--spacing-md);
  }
  
  .page-title {
    font-size: 1.75rem;
  }
  
  .card-content,
  .card-header,
  .card-footer {
    padding: var(--spacing-md);
  }
}

/* Animation utilities */
@keyframes fadeIn {
  from { opacity: 0; transform: translateY(8px); }
  to { opacity: 1; transform: translateY(0); }
}

@keyframes fadeOut {
  from { opacity: 1; transform: translateY(0); }
  to { opacity: 0; transform: translateY(-8px); }
}

.fade-in {
  animation: fadeIn 0.3s ease forwards;
}

.fade-out {
  animation: fadeOut 0.2s ease forwards;
}

/* Force Light Mode - Professional Treasury Interface */
/* Override any system dark mode preferences */
@media (prefers-color-scheme: dark) {
  body {
    background: #fafafa !important;
    color: #1a1a1a !important;
  }

  .card {
    background: white !important;
    color: #1a1a1a !important;
  }

  .table-container {
    background: white !important;
    border-color: var(--color-neutral-200) !important;
  }

  .table {
    background: white !important;
    color: #1a1a1a !important;
  }

  .table th {
    background: var(--color-neutral-50) !important;
    color: var(--color-neutral-700) !important;
    border-color: var(--color-neutral-200) !important;
  }

  .table td {
    background: white !important;
    color: var(--color-neutral-700) !important;
    border-color: var(--color-neutral-200) !important;
  }

  .table tbody tr:hover {
    background: var(--color-neutral-50) !important;
  }

  .table tbody tr:hover td {
    background: var(--color-neutral-50) !important;
  }
}