/**
 * DAILY CASH MANAGEMENT CSS
 * 
 * Micro-Job 2.1.5: Basic UI Component Structure Styling
 * 
 * Professional styling for the Daily Cash Management component including:
 * - Summary dashboard cards
 * - Loading and error states
 * - Header and action buttons
 * - Responsive design
 * - Color coding for discrepancies and status indicators
 */

/* =============================================
 * MAIN CONTAINER
 * ============================================= */

.daily-cash-management {
  padding: 24px;
  background-color: #f8fafc;
  min-height: 100vh;
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
}

/* =============================================
 * HEADER SECTION
 * ============================================= */

.daily-cash-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 32px;
  background: white;
  padding: 24px;
  border-radius: 12px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  border: 1px solid #e2e8f0;
}

.header-content h1 {
  color: #1e293b;
  font-size: 28px;
  font-weight: 700;
  margin: 0 0 8px 0;
  display: flex;
  align-items: center;
  gap: 12px;
}

.header-content h1 i {
  color: #3b82f6;
  font-size: 24px;
}

.header-content p {
  color: #64748b;
  font-size: 16px;
  margin: 0;
}

.header-actions {
  display: flex;
  gap: 12px;
}

.action-button {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 12px 20px;
  border: none;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
  text-decoration: none;
}

.action-button:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.action-button.primary {
  background: #3b82f6;
  color: white;
}

.action-button.primary:hover:not(:disabled) {
  background: #2563eb;
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);
}

.action-button.secondary {
  background: #f1f5f9;
  color: #475569;
  border: 1px solid #e2e8f0;
}

.action-button.secondary:hover:not(:disabled) {
  background: #e2e8f0;
  transform: translateY(-1px);
}

/* =============================================
 * SUMMARY CARDS GRID
 * ============================================= */

.daily-cash-summary-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));
  gap: 24px;
  margin-bottom: 32px;
}

.summary-card {
  background: white;
  border-radius: 12px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  border: 1px solid #e2e8f0;
  overflow: hidden;
  transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.summary-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px 24px 16px 24px;
  border-bottom: 1px solid #f1f5f9;
}

.card-header h3 {
  color: #1e293b;
  font-size: 18px;
  font-weight: 600;
  margin: 0;
}

.card-header i {
  color: #64748b;
  font-size: 20px;
}

.card-content {
  padding: 16px 24px 24px 24px;
}

.stat-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}

.stat-row:last-child {
  margin-bottom: 0;
}

.stat-row span:first-child {
  color: #64748b;
  font-size: 14px;
  font-weight: 500;
}

.stat-value {
  color: #1e293b;
  font-size: 16px;
  font-weight: 600;
}

.stat-value-small {
  color: #64748b;
  font-size: 12px;
  font-weight: 500;
}

/* =============================================
 * COLOR CODING
 * ============================================= */

.text-green-600 {
  color: #059669 !important;
}

.text-yellow-600 {
  color: #d97706 !important;
}

.text-red-600 {
  color: #dc2626 !important;
}

.text-blue-600 {
  color: #2563eb !important;
}

/* =============================================
 * SERVICE STATUS INDICATORS
 * ============================================= */

.service-status {
  margin-top: 16px;
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.service-indicator {
  display: flex;
  align-items: center;
  font-size: 12px;
  font-weight: 500;
  padding: 6px 12px;
  border-radius: 20px;
  position: relative;
}

.service-indicator::before {
  content: '';
  width: 8px;
  height: 8px;
  border-radius: 50%;
  margin-right: 8px;
}

.service-indicator.active {
  background: #dcfce7;
  color: #166534;
}

.service-indicator.active::before {
  background: #22c55e;
}

.service-indicator.inactive {
  background: #fef2f2;
  color: #991b1b;
}

.service-indicator.inactive::before {
  background: #ef4444;
}

/* =============================================
 * TABLE SECTION
 * ============================================= */

.daily-cash-table-section {
  background: white;
  border-radius: 12px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  border: 1px solid #e2e8f0;
  overflow: hidden;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 24px;
  border-bottom: 1px solid #f1f5f9;
}

.section-header h2 {
  color: #1e293b;
  font-size: 20px;
  font-weight: 600;
  margin: 0;
}

.entry-count {
  color: #64748b;
  font-size: 14px;
  font-weight: 500;
  padding: 6px 12px;
  background: #f1f5f9;
  border-radius: 20px;
}

.table-placeholder {
  padding: 60px 24px;
  text-align: center;
  color: #64748b;
}

.table-placeholder i {
  font-size: 48px;
  color: #cbd5e1;
  margin-bottom: 16px;
}

.table-placeholder p {
  font-size: 16px;
  margin: 8px 0;
}

.placeholder-detail {
  font-size: 14px;
  color: #94a3b8;
  max-width: 600px;
  margin: 16px auto 0 auto;
  line-height: 1.5;
}

/* =============================================
 * LOADING AND ERROR STATES
 * ============================================= */

.daily-cash-loading,
.daily-cash-error {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 50vh;
  padding: 40px;
}

.loading-spinner {
  margin-bottom: 16px;
}

.loading-spinner i {
  font-size: 32px;
  color: #3b82f6;
}

.daily-cash-loading p {
  color: #64748b;
  font-size: 16px;
  font-weight: 500;
}

.error-content {
  text-align: center;
  max-width: 400px;
}

.error-content i {
  font-size: 48px;
  color: #ef4444;
  margin-bottom: 16px;
}

.error-content h3 {
  color: #1e293b;
  font-size: 20px;
  font-weight: 600;
  margin: 0 0 12px 0;
}

.error-content p {
  color: #64748b;
  font-size: 16px;
  margin: 0 0 24px 0;
}

.retry-button {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 12px 24px;
  background: #3b82f6;
  color: white;
  border: none;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
}

.retry-button:hover {
  background: #2563eb;
  transform: translateY(-1px);
}

/* =============================================
 * MODAL STYLES (PLACEHOLDER)
 * ============================================= */

.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.verification-modal {
  background: white;
  border-radius: 12px;
  padding: 24px;
  max-width: 500px;
  width: 90%;
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.3);
}

.verification-modal h3 {
  color: #1e293b;
  font-size: 18px;
  font-weight: 600;
  margin: 0 0 16px 0;
}

.verification-modal p {
  color: #64748b;
  margin: 0 0 20px 0;
}

.verification-modal button {
  padding: 10px 20px;
  background: #3b82f6;
  color: white;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  font-weight: 500;
}

.verification-modal button:hover {
  background: #2563eb;
}

/* =============================================
 * RESPONSIVE DESIGN
 * ============================================= */

@media (max-width: 768px) {
  .daily-cash-management {
    padding: 16px;
  }

  .daily-cash-header {
    flex-direction: column;
    gap: 20px;
    align-items: flex-start;
  }

  .header-actions {
    width: 100%;
    justify-content: stretch;
  }

  .action-button {
    flex: 1;
    justify-content: center;
  }

  .daily-cash-summary-grid {
    grid-template-columns: 1fr;
    gap: 16px;
    margin-bottom: 24px;
  }

  .section-header {
    flex-direction: column;
    gap: 12px;
    align-items: flex-start;
  }

  .header-content h1 {
    font-size: 24px;
  }

  .card-header h3 {
    font-size: 16px;
  }

  .stat-value {
    font-size: 14px;
  }
}

@media (max-width: 480px) {
  .daily-cash-management {
    padding: 12px;
  }

  .daily-cash-header {
    padding: 20px;
  }

  .summary-card {
    margin: 0;
  }

  .card-header,
  .card-content {
    padding: 16px 20px;
  }

  .service-status {
    margin-top: 12px;
  }

  .service-indicator {
    font-size: 11px;
    padding: 5px 10px;
  }
}

/* =============================================
 * PRINT STYLES
 * ============================================= */

@media print {
  .daily-cash-management {
    background: white;
    padding: 0;
  }

  .header-actions,
  .action-button {
    display: none;
  }

  .summary-card {
    break-inside: avoid;
    box-shadow: none;
    border: 1px solid #ccc;
  }

  .table-placeholder {
    display: none;
  }
}

/* Daily Cash Management Table Styles */

/* Empty State */
.empty-state {
  text-align: center;
  padding: 3rem;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  margin-top: 1rem;
}

.empty-state i {
  font-size: 3rem;
  color: #9ca3af;
  margin-bottom: 1rem;
}

.empty-state h3 {
  margin: 1rem 0;
  color: #374151;
  font-size: 1.25rem;
}

.empty-state p {
  color: #6b7280;
  margin-bottom: 1.5rem;
}

/* Table Container */
.table-container {
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  margin-top: 1rem;
  overflow: hidden;
}

.table-wrapper {
  overflow-x: auto;
  max-width: 100%;
}

/* Daily Cash Table */
.daily-cash-table {
  width: 100%;
  border-collapse: collapse;
  font-size: 0.875rem;
  line-height: 1.25rem;
  min-width: 1600px; /* Ensure minimum width for all columns */
}

.daily-cash-table thead {
  background: linear-gradient(135deg, #1e40af 0%, #3b82f6 100%);
  color: white;
}

.daily-cash-table th {
  padding: 0.75rem 0.5rem;
  text-align: left;
  font-weight: 600;
  border-right: 1px solid rgba(255, 255, 255, 0.2);
  position: sticky;
  top: 0;
  z-index: 10;
}

.daily-cash-table th:last-child {
  border-right: none;
}

.daily-cash-table th small {
  display: block;
  font-weight: 400;
  font-size: 0.75rem;
  opacity: 0.8;
  margin-top: 0.25rem;
}

/* Column Widths */
.col-date { width: 100px; min-width: 90px; }
.col-bank { width: 140px; min-width: 120px; }
.col-account { width: 120px; min-width: 100px; }
.col-currency { width: 80px; min-width: 70px; }
.col-balance { width: 120px; min-width: 110px; }
.col-cash-in { width: 110px; min-width: 100px; }
.col-cash-out { width: 110px; min-width: 100px; }
.col-interco { width: 100px; min-width: 90px; }
.col-deposit { width: 110px; min-width: 100px; }
.col-discrepancy { width: 120px; min-width: 110px; }
.col-notes { width: 150px; min-width: 130px; }
.col-verified { width: 100px; min-width: 90px; }

/* Table Body */
.daily-cash-table tbody tr {
  border-bottom: 1px solid #e5e7eb;
  transition: background-color 0.2s ease;
}

.daily-cash-table tbody tr:hover {
  background-color: #f9fafb;
}

.daily-cash-table tbody tr.verified-row {
  background-color: #f0f9ff;
}

.daily-cash-table tbody tr.verified-row:hover {
  background-color: #e0f2fe;
}

.daily-cash-table td {
  padding: 0.75rem 0.5rem;
  border-right: 1px solid #e5e7eb;
  vertical-align: top;
}

.daily-cash-table td:last-child {
  border-right: none;
}

/* Cell Content Containers */
.date-cell {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.date-main {
  font-weight: 600;
  color: #374151;
}

.date-day {
  font-size: 0.75rem;
  color: #6b7280;
  text-transform: uppercase;
}

.bank-cell {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.bank-cell i {
  color: #3b82f6;
  font-size: 0.875rem;
}

.account-cell {
  font-family: 'Courier New', monospace;
  font-size: 0.8rem;
  color: #374151;
}

.currency-badge {
  background: #dbeafe;
  color: #1e40af;
  padding: 0.25rem 0.5rem;
  border-radius: 4px;
  font-weight: 600;
  font-size: 0.75rem;
}

.balance-cell {
  text-align: right;
}

.balance-amount {
  font-weight: 600;
  font-family: 'Courier New', monospace;
}

.balance-amount.positive {
  color: #059669;
}

.balance-amount.negative {
  color: #dc2626;
}

.balance-amount.projected {
  opacity: 0.7;
  font-style: italic;
}

.cash-flow-cell {
  display: flex;
  align-items: center;
  justify-content: flex-end;
  gap: 0.25rem;
  font-family: 'Courier New', monospace;
  font-weight: 600;
}

.cash-flow-cell.positive {
  color: #059669;
}

.cash-flow-cell.negative {
  color: #dc2626;
}

.cash-flow-cell i {
  font-size: 0.75rem;
}

.interco-cell {
  display: flex;
  align-items: center;
  justify-content: flex-end;
  gap: 0.25rem;
  font-family: 'Courier New', monospace;
  font-weight: 600;
}

.interco-cell.positive {
  color: #059669;
}

.interco-cell.negative {
  color: #dc2626;
}

.interco-cell i {
  color: #8b5cf6;
  font-size: 0.75rem;
}

.deposit-cell {
  display: flex;
  align-items: center;
  justify-content: flex-end;
  gap: 0.25rem;
  font-family: 'Courier New', monospace;
  font-weight: 600;
}

.deposit-cell.positive {
  color: #059669;
}

.deposit-cell.negative {
  color: #dc2626;
}

.deposit-cell i {
  color: #f59e0b;
  font-size: 0.75rem;
}

.discrepancy-cell {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 0.25rem;
  text-align: center;
}

.discrepancy-cell i {
  font-size: 0.75rem;
}

.discrepancy-cell.green {
  color: #059669;
}

.discrepancy-cell.yellow {
  color: #d97706;
}

.discrepancy-cell.red {
  color: #dc2626;
}

.discrepancy-badge {
  background: #fef3c7;
  color: #92400e;
  padding: 0.125rem 0.25rem;
  border-radius: 3px;
  font-size: 0.625rem;
  font-weight: 600;
  text-transform: uppercase;
}

.discrepancy-cell.red .discrepancy-badge {
  background: #fee2e2;
  color: #991b1b;
}

.notes-cell {
  max-width: 150px;
}

.notes-content {
  display: flex;
  align-items: center;
  gap: 0.25rem;
  margin-bottom: 0.25rem;
  font-size: 0.8rem;
}

.notes-content i {
  color: #6b7280;
  font-size: 0.75rem;
  flex-shrink: 0;
}

.notes-content span {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.no-notes {
  color: #9ca3af;
  font-style: italic;
}

.verification-cell {
  text-align: center;
}

.verified-status {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 0.25rem;
}

.verified-status i {
  color: #059669;
  font-size: 1.25rem;
}

.verified-text {
  color: #059669;
  font-weight: 600;
  font-size: 0.75rem;
}

.verified-details {
  display: flex;
  flex-direction: column;
  gap: 0.125rem;
}

.verified-details small {
  color: #6b7280;
  font-size: 0.625rem;
}

.verify-button {
  background: #3b82f6;
  color: white;
  border: none;
  padding: 0.375rem 0.75rem;
  border-radius: 4px;
  font-size: 0.75rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  gap: 0.25rem;
  justify-content: center;
}

.verify-button:hover {
  background: #2563eb;
  transform: translateY(-1px);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.verify-button i {
  font-size: 0.75rem;
}

/* Table Footer */
.table-footer {
  border-top: 2px solid #e5e7eb;
  background: #f9fafb;
  padding: 1rem;
}

.table-summary {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1rem;
}

.summary-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.5rem;
  background: white;
  border-radius: 4px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.summary-item span:first-child {
  font-weight: 500;
  color: #374151;
}

.summary-item span:last-child {
  font-weight: 600;
  font-family: 'Courier New', monospace;
}

/* Responsive Design */
@media (max-width: 1200px) {
  .table-wrapper {
    box-shadow: inset 0 0 0 1px #e5e7eb;
  }
  
  .daily-cash-table {
    font-size: 0.8rem;
  }
  
  .col-date, .col-currency, .col-verified { width: 80px; min-width: 70px; }
  .col-bank, .col-account { width: 100px; min-width: 90px; }
  .col-balance, .col-cash-in, .col-cash-out, .col-interco, .col-deposit, .col-discrepancy { width: 90px; min-width: 80px; }
  .col-notes { width: 120px; min-width: 100px; }
}

@media (max-width: 768px) {
  .table-container {
    margin: 0 -1rem;
    border-radius: 0;
  }
  
  .daily-cash-table {
    font-size: 0.75rem;
  }
  
  .daily-cash-table th,
  .daily-cash-table td {
    padding: 0.5rem 0.25rem;
  }
  
  .summary-item {
    font-size: 0.875rem;
  }
}

/* Loading and Error States */
.table-loading {
  text-align: center;
  padding: 2rem;
  color: #6b7280;
}

.table-error {
  text-align: center;
  padding: 2rem;
  color: #dc2626;
  background: #fee2e2;
  border-radius: 8px;
  margin: 1rem 0;
}

/* Accessibility */
.daily-cash-table th:focus,
.verify-button:focus {
  outline: 2px solid #3b82f6;
  outline-offset: 2px;
}

/* Print Styles */
@media print {
  .daily-cash-table {
    font-size: 0.7rem;
  }
  
  .verify-button {
    display: none;
  }
  
  .table-footer {
    break-inside: avoid;
  }
} 