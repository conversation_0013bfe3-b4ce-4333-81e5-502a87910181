/* Bank Account Manager Styles */
.bank-account-manager {
  max-width: 1200px;
  margin: 0 auto;
  padding: var(--spacing-lg);
}

.manager-header {
  text-align: center;
  margin-bottom: var(--spacing-2xl);
}

.manager-header h2 {
  font-size: 2rem;
  font-weight: 700;
  color: var(--color-neutral-900);
  margin-bottom: var(--spacing-sm);
}

.manager-header p {
  font-size: 1.125rem;
  color: var(--color-neutral-600);
  margin: 0;
}

/* Accounts Section */
.accounts-section {
  background: white;
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-sm);
  border: 1px solid var(--color-neutral-200);
  padding: var(--spacing-xl);
  margin-bottom: var(--spacing-xl);
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--spacing-xl);
  padding-bottom: var(--spacing-lg);
  border-bottom: 1px solid var(--color-neutral-200);
}

.section-header h3 {
  font-size: 1.5rem;
  font-weight: 600;
  color: var(--color-neutral-900);
  margin: 0;
}

.section-actions {
  display: flex;
  gap: var(--spacing-sm);
  align-items: center;
}

/* Empty State */
.empty-state {
  text-align: center;
  padding: var(--spacing-3xl);
  color: var(--color-neutral-500);
}

.empty-icon {
  width: 64px;
  height: 64px;
  margin: 0 auto var(--spacing-lg);
  display: flex;
  align-items: center;
  justify-content: center;
  background: var(--color-neutral-100);
  border-radius: var(--radius-xl);
  color: var(--color-neutral-400);
}

.empty-state h3 {
  font-size: 1.5rem;
  font-weight: 600;
  color: var(--color-neutral-700);
  margin-bottom: var(--spacing-sm);
}

.empty-state p {
  font-size: 1rem;
  color: var(--color-neutral-500);
  margin: 0;
}

/* Accounts Grid */
.accounts-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(320px, 1fr));
  gap: var(--spacing-lg);
}

.account-card {
  background: var(--color-neutral-50);
  border: 1px solid var(--color-neutral-200);
  border-radius: var(--radius-lg);
  padding: var(--spacing-lg);
  transition: all 0.2s ease;
}

.account-card:hover {
  box-shadow: var(--shadow-md);
  border-color: var(--color-neutral-300);
}

.account-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--spacing-md);
  padding-bottom: var(--spacing-md);
  border-bottom: 1px solid var(--color-neutral-200);
}

.account-header h4 {
  font-size: 1.125rem;
  font-weight: 600;
  color: var(--color-neutral-900);
  margin: 0;
  flex: 1;
  min-width: 0;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.account-actions {
  display: flex;
  gap: var(--spacing-xs);
  flex-shrink: 0;
}

.btn-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
  border: none;
  background: var(--color-neutral-200);
  color: var(--color-neutral-600);
  border-radius: var(--radius-sm);
  cursor: pointer;
  transition: all 0.2s ease;
}

.btn-icon:hover {
  background: var(--color-neutral-300);
  color: var(--color-neutral-700);
}

.btn-icon.btn-danger:hover {
  background: var(--color-error);
  color: white;
}

.account-details p {
  margin: var(--spacing-xs) 0;
  color: var(--color-neutral-700);
  font-size: 0.875rem;
}

.account-details p:first-child {
  margin-top: 0;
}

.account-details p:last-child {
  margin-bottom: 0;
}

.account-details .balance {
  font-weight: 600;
  color: var(--color-success);
  font-family: var(--font-mono);
}

/* Account Form Section */
.account-form-section {
  background: white;
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-sm);
  border: 1px solid var(--color-neutral-200);
  padding: var(--spacing-xl);
}

.form-header {
  margin-bottom: var(--spacing-xl);
  padding-bottom: var(--spacing-lg);
  border-bottom: 1px solid var(--color-neutral-200);
}

.form-header h3 {
  font-size: 1.5rem;
  font-weight: 600;
  color: var(--color-neutral-900);
  margin: 0;
}

.account-form {
  max-width: 800px;
}

.form-row {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: var(--spacing-lg);
  margin-bottom: var(--spacing-lg);
}

.form-row:last-of-type {
  grid-template-columns: 1fr;
}

.form-input.error,
.form-select.error {
  border-color: var(--color-error);
  box-shadow: 0 0 0 3px rgba(239, 68, 68, 0.1);
}

.form-error-message {
  background: var(--color-error);
  color: white;
  padding: var(--spacing-md);
  border-radius: var(--radius-md);
  margin-bottom: var(--spacing-lg);
  font-weight: 500;
}

.form-actions {
  display: flex;
  justify-content: flex-end;
  gap: var(--spacing-md);
  margin-top: var(--spacing-xl);
  padding-top: var(--spacing-lg);
  border-top: 1px solid var(--color-neutral-200);
}

/* Responsive Design */
@media (max-width: 1024px) {
  .accounts-grid {
    grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
    gap: var(--spacing-md);
  }
  
  .section-header {
    flex-direction: column;
    align-items: stretch;
    gap: var(--spacing-md);
  }
}

@media (max-width: 768px) {
  .bank-account-manager {
    padding: var(--spacing-md);
  }
  
  .accounts-section,
  .account-form-section {
    padding: var(--spacing-lg);
  }
  
  .accounts-grid {
    grid-template-columns: 1fr;
  }
  
  .form-row {
    grid-template-columns: 1fr;
    gap: var(--spacing-md);
  }
  
  .form-actions {
    flex-direction: column;
  }
  
  .form-actions .btn {
    width: 100%;
  }
  
  .account-header {
    flex-direction: column;
    align-items: flex-start;
    gap: var(--spacing-sm);
  }
  
  .account-actions {
    align-self: flex-end;
  }
} 