{"name": "treasury-management-system", "version": "1.0.0", "description": "Local Treasury Management System", "private": true, "dependencies": {"@tensorflow/tfjs": "^4.22.0", "@tensorflow/tfjs-node": "^4.22.0", "@types/node": "^16.18.68", "@types/react": "^18.2.43", "@types/react-dom": "^18.2.17", "natural": "^8.1.0", "react": "^18.2.0", "react-dom": "^18.2.0", "react-scripts": "5.0.1", "typescript": "^4.9.5", "uuid": "^9.0.1", "web-vitals": "^2.1.4"}, "scripts": {"start": "react-scripts start", "build": "react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "devDependencies": {"@craco/craco": "^7.1.0", "@testing-library/jest-dom": "^5.17.0", "@testing-library/react": "^13.4.0", "@testing-library/user-event": "^13.5.0", "buffer": "^6.0.3", "crypto-browserify": "^3.12.1", "os-browserify": "^0.3.0", "path-browserify": "^1.0.1", "process": "^0.11.10", "stream-browserify": "^3.0.0", "url": "^0.11.4", "util": "^0.12.5"}}