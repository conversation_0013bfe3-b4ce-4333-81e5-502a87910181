.settings-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 20px;
  background: #f8f9fa;
  border-radius: 12px;
  min-height: 600px;
}

.settings-header {
  text-align: center;
  margin-bottom: 32px;
  padding-bottom: 16px;
  border-bottom: 2px solid #e9ecef;
}

.settings-header h2 {
  margin: 0 0 8px 0;
  color: #2c3e50;
  font-size: 2rem;
  font-weight: 600;
}

.settings-header p {
  margin: 0;
  color: #6c757d;
  font-size: 1.1rem;
}

.settings-sections {
  display: grid;
  gap: 24px;
}

.settings-section {
  background: #fff;
  border-radius: 12px;
  padding: 24px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  border: 1px solid #e9ecef;
  transition: all 0.3s ease;
}

.settings-section:hover {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.settings-section.emergency {
  border-left: 4px solid #dc3545;
  background: linear-gradient(to right, #fff5f5, #ffffff);
}

.settings-section h3 {
  margin: 0 0 16px 0;
  color: #2c3e50;
  font-size: 1.4rem;
  font-weight: 600;
  display: flex;
  align-items: center;
  gap: 8px;
}

.setting-item {
  display: grid;
  grid-template-columns: 1fr auto;
  gap: 24px;
  align-items: start;
}

.setting-info h4 {
  margin: 0 0 8px 0;
  color: #2c3e50;
  font-size: 1.1rem;
  font-weight: 600;
}

.setting-info p {
  margin: 0 0 12px 0;
  color: #6c757d;
  line-height: 1.5;
}

.warning-text {
  color: #dc3545 !important;
  font-weight: 500;
}

.status-badge {
  display: inline-block;
  padding: 6px 12px;
  border-radius: 20px;
  font-size: 0.9rem;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.status-badge.enabled {
  background: #fff3cd;
  color: #856404;
  border: 1px solid #ffeaa7;
}

.status-badge.disabled {
  background: #d1ecf1;
  color: #0c5460;
  border: 1px solid #bee5eb;
}

.setting-controls {
  display: flex;
  flex-direction: column;
  gap: 8px;
  align-items: flex-end;
}

.btn {
  padding: 10px 16px;
  border: none;
  border-radius: 6px;
  font-size: 0.95rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  min-width: 140px;
  text-align: center;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 6px;
}

.btn:hover {
  transform: translateY(-1px);
  box-shadow: 0 3px 8px rgba(0, 0, 0, 0.15);
}

.btn-primary {
  background: linear-gradient(135deg, #007bff, #0056b3);
  color: white;
}

.btn-primary:hover {
  background: linear-gradient(135deg, #0056b3, #004085);
}

.btn-secondary {
  background: #6c757d;
  color: white;
}

.btn-secondary:hover {
  background: #545b62;
}

.btn-warning {
  background: linear-gradient(135deg, #ffc107, #e0a800);
  color: #212529;
}

.btn-warning:hover {
  background: linear-gradient(135deg, #e0a800, #d39e00);
}

.btn-danger {
  background: linear-gradient(135deg, #dc3545, #c82333);
  color: white;
}

.btn-danger:hover {
  background: linear-gradient(135deg, #c82333, #bd2130);
}

.btn-info {
  background: linear-gradient(135deg, #17a2b8, #138496);
  color: white;
}

.btn-info:hover {
  background: linear-gradient(135deg, #138496, #117a8b);
}

.state-info, .performance-info {
  display: grid;
  gap: 8px;
  margin-top: 12px;
}

.state-stat, .performance-stat {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 12px;
  background: #f8f9fa;
  border-radius: 6px;
  border: 1px solid #e9ecef;
}

.state-stat .label, .performance-stat .label {
  font-weight: 500;
  color: #495057;
}

.state-stat .value, .performance-stat .value {
  font-weight: 600;
  color: #2c3e50;
}

.value.success {
  color: #28a745;
}

.value.warning {
  color: #ffc107;
}

.value.disabled {
  color: #6c757d;
}

/* Modal Styles */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.modal-content {
  background: white;
  border-radius: 12px;
  width: 90%;
  max-width: 600px;
  max-height: 80vh;
  overflow: hidden;
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.2);
}

.modal-header {
  padding: 20px 24px;
  border-bottom: 1px solid #e9ecef;
  display: flex;
  justify-content: space-between;
  align-items: center;
  background: #f8f9fa;
}

.modal-header h3 {
  margin: 0;
  color: #2c3e50;
  font-size: 1.3rem;
}

.modal-close {
  background: none;
  border: none;
  font-size: 1.5rem;
  cursor: pointer;
  color: #6c757d;
  padding: 0;
  width: 30px;
  height: 30px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  transition: all 0.2s ease;
}

.modal-close:hover {
  background: #e9ecef;
  color: #495057;
}

.modal-body {
  padding: 24px;
  max-height: 400px;
  overflow-y: auto;
}

.state-export {
  background: #f8f9fa;
  border: 1px solid #e9ecef;
  border-radius: 6px;
  padding: 16px;
  font-size: 0.85rem;
  overflow-x: auto;
  white-space: pre-wrap;
  max-height: 300px;
  margin-top: 12px;
}

.modal-footer {
  padding: 16px 24px;
  border-top: 1px solid #e9ecef;
  background: #f8f9fa;
  display: flex;
  justify-content: flex-end;
}

/* Responsive Design */
@media (max-width: 768px) {
  .settings-container {
    padding: 16px;
  }
  
  .setting-item {
    grid-template-columns: 1fr;
    gap: 16px;
  }
  
  .setting-controls {
    align-items: stretch;
  }
  
  .btn {
    min-width: unset;
  }
  
  .modal-content {
    width: 95%;
    margin: 16px;
  }
  
  .state-stat, .performance-stat {
    flex-direction: column;
    align-items: flex-start;
    gap: 4px;
  }
}

/* Animation */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.settings-section {
  animation: fadeIn 0.5s ease-out;
}

.settings-section:nth-child(1) { animation-delay: 0.1s; }
.settings-section:nth-child(2) { animation-delay: 0.2s; }
.settings-section:nth-child(3) { animation-delay: 0.3s; }
.settings-section:nth-child(4) { animation-delay: 0.4s; } 