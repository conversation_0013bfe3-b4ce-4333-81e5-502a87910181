.qwen-integration-status {
  background: #ffffff;
  border: 1px solid #e5e7eb;
  border-radius: 12px;
  padding: 24px;
  margin: 16px 0;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.status-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.status-indicator {
  display: flex;
  align-items: center;
  gap: 12px;
}

.status-icon {
  font-size: 20px;
}

.status-text {
  font-size: 16px;
  font-weight: 600;
}

.status-ready {
  color: #10B981;
}

.status-downloading {
  color: #F59E0B;
}

.status-offline {
  color: #6B7280;
}

.status-loading {
  color: #3B82F6;
}

.refresh-btn {
  background: #f3f4f6;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  padding: 8px 12px;
  cursor: pointer;
  font-size: 14px;
  transition: all 0.2s ease;
}

.refresh-btn:hover:not(:disabled) {
  background: #e5e7eb;
  border-color: #9ca3af;
}

.refresh-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.status-error {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 12px 16px;
  background: #fef2f2;
  border: 1px solid #fecaca;
  border-radius: 8px;
  margin-bottom: 20px;
}

.error-icon {
  font-size: 16px;
}

.error-text {
  color: #dc2626;
  font-size: 14px;
  font-weight: 500;
}

.performance-stats {
  margin-bottom: 24px;
}

.performance-stats h4 {
  font-size: 18px;
  font-weight: 600;
  color: #1f2937;
  margin: 0 0 16px 0;
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
  gap: 16px;
}

.stat-item {
  background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
  border: 1px solid #e2e8f0;
  border-radius: 8px;
  padding: 16px 12px;
  text-align: center;
  border-left: 3px solid #8B5CF6;
}

.stat-value {
  display: block;
  font-size: 20px;
  font-weight: 700;
  color: #1f2937;
  margin-bottom: 4px;
}

.stat-label {
  font-size: 11px;
  font-weight: 500;
  color: #8B5CF6;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.integration-info {
  margin-bottom: 24px;
}

.integration-info h4 {
  font-size: 18px;
  font-weight: 600;
  color: #1f2937;
  margin: 0 0 16px 0;
}

.info-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 12px;
}

.info-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 16px;
  background: #f9fafb;
  border: 1px solid #e5e7eb;
  border-radius: 6px;
}

.info-label {
  font-size: 14px;
  font-weight: 500;
  color: #6b7280;
}

.info-value {
  font-size: 14px;
  font-weight: 600;
  color: #1f2937;
}

.integration-benefits {
  margin-bottom: 0;
}

.integration-benefits h4 {
  font-size: 18px;
  font-weight: 600;
  color: #1f2937;
  margin: 0 0 16px 0;
}

.integration-benefits ul {
  list-style: none;
  padding: 0;
  margin: 0;
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 8px;
}

.integration-benefits li {
  padding: 8px 12px;
  background: #f0f9ff;
  border: 1px solid #bae6fd;
  border-radius: 6px;
  font-size: 14px;
  color: #0c4a6e;
  display: flex;
  align-items: center;
  gap: 8px;
}

/* Responsive Design */
@media (max-width: 768px) {
  .qwen-integration-status {
    padding: 16px;
    margin: 12px 0;
  }
  
  .status-header {
    flex-direction: column;
    gap: 12px;
    align-items: stretch;
  }
  
  .stats-grid {
    grid-template-columns: repeat(2, 1fr);
  }
  
  .info-grid {
    grid-template-columns: 1fr;
  }
  
  .integration-benefits ul {
    grid-template-columns: 1fr;
  }
}

/* Force Light Mode */
.qwen-integration-status {
  background: #ffffff !important;
  color: #1a1a1a !important;
  border-color: #e5e5e5 !important;
}

.performance-stats h4,
.integration-info h4,
.integration-benefits h4 {
  color: #1a1a1a !important;
}

.stat-item {
  background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%) !important;
  border-color: #e5e5e5 !important;
}

.stat-value {
  color: #1a1a1a !important;
}

.info-item {
  background: #f8f9fa !important;
  border-color: #e5e5e5 !important;
}

.info-label {
  color: #666666 !important;
}

.info-value {
  color: #1a1a1a !important;
}

.integration-benefits li {
  background: #f0f8ff !important;
  border-color: #d1e7ff !important;
  color: #1a365d !important;
}
