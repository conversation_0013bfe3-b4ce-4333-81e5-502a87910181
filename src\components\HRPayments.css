/* HR Payments Container */
.hr-payments-container {
  padding: 20px;
  max-width: 1400px;
  margin: 0 auto;
  background-color: #f8fafc;
  min-height: 100vh;
}

/* Header */
.hr-payments-header {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 12px;
  padding: 24px;
  margin-bottom: 24px;
  color: white;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.header-title h2 {
  margin: 0 0 8px 0;
  font-size: 28px;
  font-weight: 600;
}

.header-title p {
  margin: 0;
  opacity: 0.9;
  font-size: 16px;
}

.loading-spinner {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 14px;
  opacity: 0.9;
}

.loading-spinner::before {
  content: '';
  width: 20px;
  height: 20px;
  border: 2px solid rgba(255, 255, 255, 0.3);
  border-top: 2px solid white;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Summary Dashboard */
.summary-dashboard {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 16px;
  margin-bottom: 24px;
}

.summary-card {
  background: white;
  border-radius: 12px;
  padding: 20px;
  display: flex;
  align-items: center;
  gap: 16px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  border: 1px solid #e5e7eb;
  transition: transform 0.2s ease;
}

.summary-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
}

.summary-icon {
  font-size: 32px;
  width: 50px;
  text-align: center;
}

.summary-content h3 {
  margin: 0 0 4px 0;
  font-size: 24px;
  font-weight: 600;
  color: #1f2937;
}

.summary-content p {
  margin: 0;
  font-size: 14px;
  color: #6b7280;
}

/* Filters Section */
.filters-section {
  background: white;
  border-radius: 12px;
  padding: 20px;
  margin-bottom: 24px;
  display: flex;
  gap: 20px;
  flex-wrap: wrap;
  align-items: end;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  border: 1px solid #e5e7eb;
}

.filter-group {
  display: flex;
  flex-direction: column;
  gap: 6px;
  min-width: 200px;
}

.filter-group label {
  font-weight: 500;
  color: #374151;
  font-size: 14px;
}

.filter-group select,
.filter-group input {
  padding: 8px 12px;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  font-size: 14px;
  background: white;
  transition: border-color 0.2s ease;
}

.filter-group select:focus,
.filter-group input:focus {
  outline: none;
  border-color: #667eea;
  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

/* Payments Table Section */
.payments-table-section {
  background: white;
  border-radius: 12px;
  padding: 24px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  border: 1px solid #e5e7eb;
}

.payments-table-section h3 {
  margin: 0 0 20px 0;
  color: #1f2937;
  font-size: 20px;
  font-weight: 600;
}

.no-data {
  text-align: center;
  padding: 40px;
  color: #6b7280;
  font-style: italic;
}

.table-container {
  overflow-x: auto;
  border-radius: 8px;
  border: 1px solid #e5e7eb;
}

.payments-table {
  width: 100%;
  border-collapse: collapse;
  background: white;
}

.payments-table th {
  background: #f9fafb;
  padding: 12px;
  text-align: left;
  font-weight: 600;
  color: #374151;
  border-bottom: 1px solid #e5e7eb;
  font-size: 14px;
}

.payments-table td {
  padding: 12px;
  border-bottom: 1px solid #f3f4f6;
  vertical-align: top;
}

.payment-row {
  transition: background-color 0.2s ease;
}

.payment-row:hover {
  background-color: #f9fafb;
}

.payment-row.confirmed {
  background-color: #f0fdf4;
}

/* Employee Info */
.employee-info {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.employee-info strong {
  color: #1f2937;
  font-weight: 600;
}

.employee-info small {
  color: #6b7280;
  font-size: 12px;
}

/* Amount Column */
.amount {
  font-weight: 600;
  color: #dc2626;
  text-align: right;
}

/* Status Badges */
.status-badge {
  padding: 4px 8px;
  border-radius: 20px;
  font-size: 12px;
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.status-pending {
  background-color: #fef3c7;
  color: #d97706;
}

.status-matched {
  background-color: #dbeafe;
  color: #2563eb;
}

.status-confirmed {
  background-color: #dcfce7;
  color: #16a34a;
}

/* Payment Type Badges */
.payment-type-badge {
  padding: 4px 8px;
  border-radius: 16px;
  font-size: 11px;
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.payment-type-salary {
  background-color: #e0e7ff;
  color: #4338ca;
}

.payment-type-bonus {
  background-color: #fef3c7;
  color: #d97706;
}

.payment-type-overtime {
  background-color: #fed7d7;
  color: #e53e3e;
}

.payment-type-reimbursement {
  background-color: #d1fae5;
  color: #059669;
}

.payment-type-settlement {
  background-color: #fce7f3;
  color: #be185d;
}

/* Confidence Scores */
.confidence {
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: 600;
}

.confidence.high {
  background-color: #dcfce7;
  color: #16a34a;
}

.confidence.medium {
  background-color: #fef3c7;
  color: #d97706;
}

.confidence.low {
  background-color: #fed7d7;
  color: #dc2626;
}

/* Payroll Match */
.payroll-match {
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.payroll-match strong {
  color: #1f2937;
  font-size: 13px;
}

.payroll-match small {
  color: #6b7280;
  font-size: 11px;
}

.no-match {
  color: #9ca3af;
  font-style: italic;
  font-size: 13px;
}

/* Action Buttons */
.action-buttons {
  display: flex;
  gap: 6px;
  flex-wrap: wrap;
}

.btn-auto-reconcile,
.btn-manual-reconcile,
.btn-confirm {
  padding: 6px 12px;
  border: none;
  border-radius: 6px;
  font-size: 12px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.btn-auto-reconcile {
  background-color: #2563eb;
  color: white;
}

.btn-auto-reconcile:hover:not(:disabled) {
  background-color: #1d4ed8;
}

.btn-manual-reconcile {
  background-color: #7c3aed;
  color: white;
}

.btn-manual-reconcile:hover:not(:disabled) {
  background-color: #6d28d9;
}

.btn-confirm {
  background-color: #16a34a;
  color: white;
}

.btn-confirm:hover:not(:disabled) {
  background-color: #15803d;
}

.btn-auto-reconcile:disabled,
.btn-manual-reconcile:disabled,
.btn-confirm:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.confirmed-text {
  color: #16a34a;
  font-weight: 600;
  font-size: 12px;
}

/* Modal Styles */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  padding: 20px;
}

.reconciliation-modal {
  background: white;
  border-radius: 12px;
  width: 100%;
  max-width: 600px;
  max-height: 90vh;
  overflow-y: auto;
  box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 24px;
  border-bottom: 1px solid #e5e7eb;
}

.modal-header h3 {
  margin: 0;
  color: #1f2937;
  font-size: 20px;
  font-weight: 600;
}

.close-btn {
  background: none;
  border: none;
  font-size: 24px;
  cursor: pointer;
  color: #6b7280;
  padding: 4px;
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 6px;
  transition: background-color 0.2s ease;
}

.close-btn:hover {
  background-color: #f3f4f6;
  color: #374151;
}

.modal-body {
  padding: 24px;
  display: flex;
  flex-direction: column;
  gap: 24px;
}

.payment-details,
.payroll-selection,
.notes-section {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.payment-details h4,
.payroll-selection h4,
.notes-section h4 {
  margin: 0;
  color: #1f2937;
  font-size: 16px;
  font-weight: 600;
}

.payment-details p {
  margin: 4px 0;
  color: #374151;
  font-size: 14px;
}

.payroll-selection select {
  padding: 10px 12px;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  font-size: 14px;
  background: white;
}

.payroll-selection select:focus {
  outline: none;
  border-color: #667eea;
  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.notes-section textarea {
  padding: 10px 12px;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  font-size: 14px;
  font-family: inherit;
  resize: vertical;
}

.notes-section textarea:focus {
  outline: none;
  border-color: #667eea;
  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.modal-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
  padding: 24px;
  border-top: 1px solid #e5e7eb;
  background-color: #f9fafb;
  border-radius: 0 0 12px 12px;
}

.btn-cancel,
.btn-confirm-reconciliation {
  padding: 10px 20px;
  border: none;
  border-radius: 6px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
}

.btn-cancel {
  background-color: #f3f4f6;
  color: #374151;
}

.btn-cancel:hover {
  background-color: #e5e7eb;
}

.btn-confirm-reconciliation {
  background-color: #2563eb;
  color: white;
}

.btn-confirm-reconciliation:hover:not(:disabled) {
  background-color: #1d4ed8;
}

.btn-confirm-reconciliation:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

/* Responsive Design */
@media (max-width: 768px) {
  .hr-payments-container {
    padding: 16px;
  }
  
  .summary-dashboard {
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: 12px;
  }
  
  .summary-card {
    padding: 16px;
    flex-direction: column;
    text-align: center;
    gap: 8px;
  }
  
  .filters-section {
    flex-direction: column;
    gap: 12px;
  }
  
  .filter-group {
    min-width: auto;
  }
  
  .payments-table {
    font-size: 12px;
  }
  
  .payments-table th,
  .payments-table td {
    padding: 8px;
  }
  
  .action-buttons {
    flex-direction: column;
    gap: 4px;
  }
  
  .reconciliation-modal {
    margin: 10px;
    max-height: calc(100vh - 20px);
  }
  
  .modal-header,
  .modal-body,
  .modal-footer {
    padding: 16px;
  }
} 